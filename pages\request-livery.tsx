import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Logo from '../components/Logo';
import Dock from '../components/Dock';
import Galaxy from '../components/galaxy';
import { VscArrowLeft, VscCloudUpload, VscHome, VscArchive, VscSettingsGear, VscMail, VscChevronRight, VscChevronLeft, VscCheck } from 'react-icons/vsc';
import emailjs from '@emailjs/browser';

interface FormData {
  name: string;
  email: string;
  phone: string;
  platform: string;
  carChoice: string;
  bodyKit: string;
  wheels: string;
  designStyle: string;
  colors: string;
  additionalRequests: string;
  referenceImages: File[];
}

const RequestLiveryPage: React.FC = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    platform: '',
    carChoice: '',
    bodyKit: '',
    wheels: '',
    designStyle: '',
    colors: '',
    additionalRequests: '',
    referenceImages: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [dragActive, setDragActive] = useState(false);

  const totalSteps = 5;

  // Form steps configuration
  const steps = [
    {
      id: 1,
      title: 'Contact Information',
      description: 'Let us know how to reach you',
      icon: '👋'
    },
    {
      id: 2,
      title: 'Platform & Pricing',
      description: 'Choose your gaming platform',
      icon: '🎮'
    },
    {
      id: 3,
      title: 'Vehicle Specifications',
      description: 'Tell us about your car',
      icon: '🚗'
    },
    {
      id: 4,
      title: 'Design Details',
      description: 'Describe your vision',
      icon: '🎨'
    },
    {
      id: 5,
      title: 'Reference Images',
      description: 'Upload inspiration (optional)',
      icon: '📸'
    }
  ];

  // Initialize EmailJS and mobile detection
  useEffect(() => {
    emailjs.init("OYVQiZ2RMjNZl9_iK");
    
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768 || 'ontouchstart' in window);
    };
    
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Navigation handlers for dock
  const handleNavigation = (sectionId: string) => {
    if (sectionId === 'home') {
      window.location.href = '/';
      return;
    }
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Dock items configuration
  const dockItems = [
    {
      icon: <VscHome size={18} />,
      label: 'Home',
      onClick: () => handleNavigation('home')
    },
    {
      icon: <VscArchive size={18} />,
      label: 'Gallery',
      onClick: () => window.location.href = '/#livery-showcase'
    },
    {
      icon: <VscSettingsGear size={18} />,
      label: 'Services',
      onClick: () => window.location.href = '/#services'
    },
    {
      icon: <VscMail size={18} />,
      label: 'Request',
      onClick: () => handleNavigation('request-form')
    },
  ];

  // Step validation
  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return formData.name.trim() !== '' && formData.email.trim() !== '';
      case 2:
        return formData.platform !== '';
      case 3:
        return formData.carChoice.trim() !== '';
      case 4:
        return formData.designStyle.trim() !== '' && formData.colors.trim() !== '';
      case 5:
        return true; // Reference images are optional
      default:
        return false;
    }
  };

  // Step navigation
  const handleNextStep = () => {
    if (currentStep < totalSteps && validateStep(currentStep)) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(prev => prev + 1);
        setIsAnimating(false);
      }, 150);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(prev => prev - 1);
        setIsAnimating(false);
      }, 150);
    }
  };

  const handleStepClick = (step: number) => {
    if (step <= currentStep || validateStep(step - 1)) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(step);
        setIsAnimating(false);
      }, 150);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        referenceImages: [...prev.referenceImages, ...fileArray].slice(0, 5)
      }));
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files);
    }
  };

  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      referenceImages: prev.referenceImages.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const templateParams = {
        to_email: '<EMAIL>',
        from_name: formData.name,
        from_email: formData.email,
        phone: formData.phone || 'Not provided',
        platform: formData.platform,
        price: getPrice(),
        car_choice: formData.carChoice,
        body_kit: formData.bodyKit || 'Not specified',
        wheels: formData.wheels || 'Not specified',
        design_style: formData.designStyle,
        colors: formData.colors,
        additional_requests: formData.additionalRequests || 'None',
        has_images: formData.referenceImages.length > 0 ? 'Yes' : 'No',
        image_count: formData.referenceImages.length,
        image_note: formData.referenceImages.length > 0 
          ? `Customer has ${formData.referenceImages.length} reference image(s). Please contact them directly for images.`
          : 'No reference images provided.',
        submission_date: new Date().toLocaleString(),
      };

      const result = await emailjs.send(
        'service_tbnov0u',
        'template_livery_request',
        templateParams
      );

      if (result.status === 200) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          phone: '',
          platform: '',
          carChoice: '',
          bodyKit: '',
          wheels: '',
          designStyle: '',
          colors: '',
          additionalRequests: '',
          referenceImages: []
        });
        setCurrentStep(1);
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('EmailJS Error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPrice = () => {
    if (formData.platform === 'console') return '$20 USD';
    if (formData.platform === 'pc') return '$35 USD';
    return '';
  };

  // Render step content
  const renderStepContent = () => {
    const baseClassName = `transition-all duration-300 ${
      isAnimating ? 'opacity-0 transform translate-x-4' : 'opacity-100 transform translate-x-0'
    }`;

    switch (currentStep) {
      case 1:
        return (
          <div className={baseClassName}>
            <div className="space-y-6">
              <div className="text-center mb-8">
                <div className="text-6xl mb-4">👋</div>
                <h3 className="text-3xl font-bold text-white mb-2">Contact Information</h3>
                <p className="text-gray-400">Let us know how to reach you</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                    Your Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-4 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 text-lg"
                    placeholder="Your full name"
                  />
                </div>
                
                <div className="md:col-span-2">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-4 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 text-lg"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div className="md:col-span-2">
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number (Optional)
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 text-lg"
                    placeholder="+****************"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className={baseClassName}>
            <div className="space-y-6">
              <div className="text-center mb-8">
                <div className="text-6xl mb-4">🎮</div>
                <h3 className="text-3xl font-bold text-white mb-2">Platform & Pricing</h3>
                <p className="text-gray-400">Choose your gaming platform</p>
              </div>

              <div className="space-y-4">
                <label className="block text-sm font-medium text-gray-300 mb-4">
                  Choose Platform *
                </label>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Console Option */}
                  <label className={`relative cursor-pointer group ${formData.platform === 'console' ? 'ring-2 ring-blue-500' : ''}`}>
                    <input
                      type="radio"
                      name="platform"
                      value="console"
                      checked={formData.platform === 'console'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`p-6 rounded-xl border-2 transition-all duration-300 ${
                      formData.platform === 'console' 
                        ? 'border-blue-500 bg-blue-500/10' 
                        : 'border-gray-600 bg-gray-800/50 hover:border-blue-400'
                    }`}>
                      <div className="text-center">
                        <div className="text-4xl mb-2">🎮</div>
                        <h4 className="text-xl font-bold text-white mb-2">Console</h4>
                        <div className="text-3xl font-bold text-blue-400 mb-2">$20 USD</div>
                        <p className="text-sm text-gray-400 mb-4">Optimized for console limits</p>
                        <ul className="text-xs text-gray-300 space-y-1">
                          <li>• Maximized detail within limits</li>
                          <li>• Professional quality design</li>
                          <li>• Step-by-step updates</li>
                        </ul>
                      </div>
                    </div>
                  </label>

                  {/* PC Option */}
                  <label className={`relative cursor-pointer group ${formData.platform === 'pc' ? 'ring-2 ring-yellow-500' : ''}`}>
                    <input
                      type="radio"
                      name="platform"
                      value="pc"
                      checked={formData.platform === 'pc'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`p-6 rounded-xl border-2 transition-all duration-300 ${
                      formData.platform === 'pc' 
                        ? 'border-yellow-500 bg-yellow-500/10' 
                        : 'border-gray-600 bg-gray-800/50 hover:border-yellow-400'
                    }`}>
                      <div className="text-center">
                        <div className="text-4xl mb-2">💻</div>
                        <h4 className="text-xl font-bold text-white mb-2">PC</h4>
                        <div className="text-3xl font-bold text-yellow-400 mb-2">$35 USD</div>
                        <p className="text-sm text-gray-400 mb-4">Premium detailed design</p>
                        <ul className="text-xs text-gray-300 space-y-1">
                          <li>• Up to 5000 layers</li>
                          <li>• Intricate details & fine touches</li>
                          <li>• Blender liveries available</li>
                        </ul>
                      </div>
                    </div>
                  </label>
                </div>

                {getPrice() && (
                  <div className="text-center p-4 bg-gradient-to-r from-yellow-400/10 to-orange-500/10 rounded-xl border border-yellow-500/20">
                    <p className="text-xl font-bold text-yellow-400">Selected Price: {getPrice()}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className={baseClassName}>
            <div className="space-y-6">
              <div className="text-center mb-8">
                <div className="text-6xl mb-4">🚗</div>
                <h3 className="text-3xl font-bold text-white mb-2">Vehicle Specifications</h3>
                <p className="text-gray-400">Tell us about your car</p>
              </div>

              <div className="space-y-6">
                <div>
                  <label htmlFor="carChoice" className="block text-sm font-medium text-gray-300 mb-2">
                    Car of Choice *
                  </label>
                  <input
                    type="text"
                    id="carChoice"
                    name="carChoice"
                    value={formData.carChoice}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-4 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 text-lg"
                    placeholder="e.g., BMW E46"
                  />
                </div>
                
                <div>
                  <label htmlFor="bodyKit" className="block text-sm font-medium text-gray-300 mb-2">
                    Body Kit (Optional)
                  </label>
                  <input
                    type="text"
                    id="bodyKit"
                    name="bodyKit"
                    value={formData.bodyKit}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 text-lg"
                    placeholder="e.g., R&T"
                  />
                </div>
                
                <div>
                  <label htmlFor="wheels" className="block text-sm font-medium text-gray-300 mb-2">
                    Wheels (Optional - Don&apos;t matter for design)
                  </label>
                  <input
                    type="text"
                    id="wheels"
                    name="wheels"
                    value={formData.wheels}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 text-lg"
                    placeholder="Any specific wheel preference"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className={baseClassName}>
            <div className="space-y-6">
              <div className="text-center mb-8">
                <div className="text-6xl mb-4">🎨</div>
                <h3 className="text-3xl font-bold text-white mb-2">Design Details</h3>
                <p className="text-gray-400">Describe your vision</p>
              </div>

              <div className="space-y-6">
                <div>
                  <label htmlFor="designStyle" className="block text-sm font-medium text-gray-300 mb-2">
                    Design Style *
                  </label>
                  <input
                    type="text"
                    id="designStyle"
                    name="designStyle"
                    value={formData.designStyle}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-4 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 text-lg"
                    placeholder="e.g., Comp style, drift style, racing, etc."
                  />
                </div>
                
                <div>
                  <label htmlFor="colors" className="block text-sm font-medium text-gray-300 mb-2">
                    Colors *
                  </label>
                  <input
                    type="text"
                    id="colors"
                    name="colors"
                    value={formData.colors}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-4 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 text-lg"
                    placeholder="e.g., Green, blue and white, etc."
                  />
                </div>
                
                <div>
                  <label htmlFor="additionalRequests" className="block text-sm font-medium text-gray-300 mb-2">
                    Additional Requests
                  </label>
                  <textarea
                    id="additionalRequests"
                    name="additionalRequests"
                    value={formData.additionalRequests}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-4 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 resize-none text-lg"
                    placeholder="Anything else you want to add or take away from your design? Any specific logos, sponsors, or modifications?"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className={baseClassName}>
            <div className="space-y-6">
              <div className="text-center mb-8">
                <div className="text-6xl mb-4">📸</div>
                <h3 className="text-3xl font-bold text-white mb-2">Reference Images</h3>
                <p className="text-gray-400">Upload inspiration (optional)</p>
              </div>

              <div 
                className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
                  dragActive 
                    ? 'border-yellow-400 bg-yellow-400/10' 
                    : 'border-gray-600 hover:border-gray-500'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <VscCloudUpload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-300 mb-4 text-lg">
                  Upload reference photos/designs (optional)
                </p>
                <label className="inline-block cursor-pointer">
                  <span className="bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-black font-bold px-8 py-3 rounded-xl transition-all duration-300 text-lg">
                    Browse Files
                  </span>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleFileUpload(e.target.files)}
                    className="hidden"
                  />
                </label>
                <p className="text-sm text-gray-500 mt-4">
                  Max 5 files, up to 10MB each (PNG, JPG, GIF)
                </p>
                <div className="mt-4 p-4 bg-blue-900/20 border border-blue-500/50 rounded-xl text-sm">
                  <p className="text-blue-300">
                    📧 <strong>Note:</strong> Reference images will be noted in your request. 
                    If you select images, Tezz will contact you directly to receive them via email.
                  </p>
                </div>
              </div>
              
              {formData.referenceImages.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                  {formData.referenceImages.map((file, index) => (
                    <div key={index} className="relative group">
                      <div className="bg-gray-800 rounded-lg p-3 border border-gray-600">
                        <div className="text-xs text-gray-300 truncate">{file.name}</div>
                        <div className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeFile(index)}
                        className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs flex items-center justify-center transition-colors duration-300"
                        aria-label={`Remove ${file.name}`}
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* Important Notes */}
              <div className="grid md:grid-cols-2 gap-6 mt-8">
                {/* Copyright Notice */}
                <div className="p-6 bg-red-900/20 border border-red-500/50 rounded-xl">
                  <h4 className="text-lg font-bold text-red-400 mb-3">⚠️ Copyright Notice</h4>
                  <p className="text-red-300 text-sm leading-relaxed">
                    I cannot do 100% copies or dupes of a livery that you send me unless you or I can get permission from the original owner to do so!
                  </p>
                </div>

                {/* Important Notes */}
                <div className="p-6 bg-gradient-to-r from-gray-800/50 to-gray-900/50 rounded-xl border border-gray-600">
                  <h4 className="text-lg font-bold text-white mb-3">📌 Important Notes</h4>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li className="flex items-start">
                      <span className="text-yellow-400 mr-2">•</span>
                      <span>Every design includes my unique artistic touches</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-yellow-400 mr-2">•</span>
                      <span>Once completed, you own the livery completely</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-yellow-400 mr-2">•</span>
                      <span>Payment required before work begins</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Payment Information */}
              <div className="p-6 bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border border-yellow-500/50 rounded-xl">
                <h4 className="text-xl font-bold text-yellow-400 mb-4">💳 Payment Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                  <div>
                    <h5 className="font-bold text-white mb-2">New Zealand Bank Transfer:</h5>
                    <p className="text-gray-300">Account: 02-0404-0278295-083</p>
                    <p className="text-gray-400">(NZ customers only)</p>
                  </div>
                  <div>
                    <h5 className="font-bold text-white mb-2">PayPal:</h5>
                    <p className="text-gray-300"><EMAIL></p>
                    <p className="text-gray-300">OR</p>
                    <p className="text-gray-300"><EMAIL></p>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-green-900/30 border border-green-500/50 rounded text-center">
                  <p className="text-green-300 font-bold">
                    ✨ Once payment is received, your name goes onto the livery list! ✨🤙
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Head>
        <title>Request Custom Livery - Tezz Designs</title>
        <meta name="description" content="Request a custom CarX livery design from Tezz Designs. Professional automotive livery design services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="bg-black text-white relative min-h-screen">
        {/* Galaxy Background */}
        <div className="fixed inset-0 w-full h-full z-0">
          <Galaxy
            focal={[0.5, 0.5]}
            rotation={[1.0, 0.0]}
            starSpeed={0.3}
            density={0.6}
            hueShift={45}
            speed={0.8}
            mouseInteraction={true}
            glowIntensity={0.4}
            saturation={0.6}
            mouseRepulsion={false}
            twinkleIntensity={0.2}
            rotationSpeed={0.05}
            repulsionStrength={1.5}
            autoCenterRepulsion={0}
            transparent={true}
          />
        </div>

        {/* Header Hero Section */}
        <section className="relative min-h-screen overflow-hidden">
          {/* Dark Overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-black/40 z-10"></div>
          
          <div className="relative z-20 min-h-screen flex flex-col">
            {/* Header Navigation */}
            <header className="py-6 px-6">
              <div className="max-w-7xl mx-auto">
                <div className="flex items-center justify-between">
                  <Link 
                    href="/"
                    className="flex items-center text-gray-300 hover:text-yellow-400 transition-colors duration-300"
                    aria-label="Go back to home page"
                  >
                    <VscArrowLeft className="w-5 h-5 mr-2" />
                    Back to Home
                  </Link>
                  
                  <div className="w-32">
                    <Logo size="medium" showText={false} />
                  </div>
                </div>
              </div>
            </header>

            {/* Hero Content */}
            <div className="flex-1 flex items-center justify-center px-6">
              <div className="max-w-4xl mx-auto text-center">
                <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent leading-tight pb-4">
                  Request Your Custom Livery
                </h1>
                <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto mb-8"></div>
                <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto mb-12 font-light leading-relaxed">
                  Transform your vision into reality with professional CarX livery design services. Let&apos;s create something extraordinary together.
                </p>
                
                {/* Scroll Indicator */}
                <div className="animate-bounce">
                  <div className="w-5 h-8 border border-yellow-400/80 rounded-full flex justify-center backdrop-blur-sm bg-black/20 mx-auto">
                    <div className="w-0.5 h-2 bg-yellow-400 rounded-full mt-1.5"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="relative py-20 px-6 z-10">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent leading-tight pb-2">
                Choose Your Platform
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto mb-6"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Select the platform that matches your gaming setup for optimized livery design
              </p>
            </div>

            {/* Pricing Cards */}
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-16">
              {/* Console Card */}
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300">
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-white mb-2">Console Livery</h3>
                    <div className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-2">
                      $20 USD
                    </div>
                    <p className="text-gray-400">Optimized for console limits</p>
                  </div>
                  <ul className="space-y-3">
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Maximized detail within layer limits
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Professional quality design
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Step-by-step updates
                    </li>
                  </ul>
                </div>
              </div>

              {/* PC Card */}
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 hover:border-yellow-500/50 transition-all duration-300">
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-white mb-2">PC Livery</h3>
                    <div className="text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">
                      $35 USD
                    </div>
                    <p className="text-gray-400">Premium detailed design</p>
                  </div>
                  <ul className="space-y-3">
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-yellow-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Up to 5000 layers available
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-yellow-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Intricate details & fine touches
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-yellow-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Blender liveries available
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Process Info */}
            <div className="max-w-4xl mx-auto">
              <div className="bg-gradient-to-r from-yellow-400/10 to-orange-500/10 rounded-2xl border border-yellow-500/20 p-8">
                <h3 className="text-2xl font-bold text-yellow-400 mb-6 text-center">🌟 What Happens Next?</h3>
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl mb-3">📝</div>
                    <p className="text-white font-semibold mb-2">1. Form Submission</p>
                    <p className="text-gray-300 text-sm">Complete the detailed form below</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl mb-3">📞</div>
                    <p className="text-white font-semibold mb-2">2. Consultation Call</p>
                    <p className="text-gray-300 text-sm">I&apos;ll reach out to discuss your vision</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl mb-3">🎨</div>
                    <p className="text-white font-semibold mb-2">3. Step-by-Step Updates</p>
                    <p className="text-gray-300 text-sm">See your livery come to life</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Form Section */}
        <section id="request-form" className="relative py-20 px-6 z-10">
          <div className="max-w-4xl mx-auto">
            {/* Success Message */}
            {submitStatus === 'success' && (
              <div className="mb-8 p-6 bg-green-900/20 border border-green-500/50 rounded-xl text-center backdrop-blur-sm animate-fade-in">
                <h3 className="text-xl font-bold text-green-400 mb-2">Request Submitted Successfully!</h3>
                <p className="text-green-300">Your name will be added to the livery list once payment is received. ✨🤙</p>
              </div>
            )}

            {/* Error Message */}
            {submitStatus === 'error' && (
              <div className="mb-8 p-6 bg-red-900/20 border border-red-500/50 rounded-xl text-center backdrop-blur-sm animate-fade-in">
                <h3 className="text-xl font-bold text-red-400 mb-2">Submission Failed</h3>
                <p className="text-red-300">Please try again or contact us directly.</p>
              </div>
            )}

            {/* Progress Indicator */}
            <div className="mb-12">
              <div className="flex items-center justify-between mb-8">
                {steps.map((step) => (
                  <div key={step.id} className="flex items-center">
                    <button
                      onClick={() => handleStepClick(step.id)}
                      className={`w-12 h-12 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                        currentStep === step.id
                          ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-black scale-110'
                          : currentStep > step.id
                          ? 'bg-green-500 text-white'
                          : validateStep(step.id - 1) || step.id === 1
                          ? 'bg-gray-600 text-white hover:bg-gray-500'
                          : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                      }`}
                      disabled={!validateStep(step.id - 1) && step.id !== 1}
                    >
                      {currentStep > step.id ? (
                        <VscCheck size={20} />
                      ) : (
                        step.id
                      )}
                    </button>
                    {step.id < totalSteps && (
                      <div className={`w-8 md:w-16 h-0.5 mx-2 transition-all duration-300 ${
                        currentStep > step.id ? 'bg-green-500' : 'bg-gray-700'
                      }`} />
                    )}
                  </div>
                ))}
              </div>

              {/* Step Labels */}
              <div className="hidden md:flex items-center justify-between text-center">
                {steps.map((step) => (
                  <div key={step.id} className="flex-1">
                    <p className={`text-sm font-medium transition-colors duration-300 ${
                      currentStep === step.id
                        ? 'text-yellow-400'
                        : currentStep > step.id
                        ? 'text-green-400'
                        : 'text-gray-500'
                    }`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-600 mt-1">{step.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit}>
              <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-2xl border border-gray-700 p-8 min-h-[600px]">
                
                {/* Step Content */}
                <div className="mb-8">
                  {renderStepContent()}
                </div>

                {/* Navigation Buttons */}
                <div className="flex justify-between items-center pt-8 border-t border-gray-700">
                  <button
                    type="button"
                    onClick={handlePrevStep}
                    disabled={currentStep === 1}
                    className={`inline-flex items-center px-6 py-3 rounded-xl font-bold transition-all duration-300 ${
                      currentStep === 1
                        ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                        : 'bg-gray-700 text-white hover:bg-gray-600'
                    }`}
                  >
                    <VscChevronLeft className="mr-2" size={18} />
                    Previous
                  </button>

                  <div className="flex items-center text-sm text-gray-400">
                    Step {currentStep} of {totalSteps}
                  </div>

                  {currentStep < totalSteps ? (
                    <button
                      type="button"
                      onClick={handleNextStep}
                      disabled={!validateStep(currentStep)}
                      className={`inline-flex items-center px-6 py-3 rounded-xl font-bold transition-all duration-300 ${
                        validateStep(currentStep)
                          ? 'bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-black transform hover:scale-105'
                          : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      Next
                      <VscChevronRight className="ml-2" size={18} />
                    </button>
                  ) : (
                    <button
                      type="submit"
                      disabled={isSubmitting || !validateStep(currentStep)}
                      className={`inline-flex items-center px-8 py-3 rounded-xl font-bold transition-all duration-300 ${
                        isSubmitting || !validateStep(currentStep)
                          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                          : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transform hover:scale-105 shadow-2xl'
                      }`}
                    >
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Submitting...
                        </>
                      ) : (
                        <>
                          Submit Request
                          <VscCheck className="ml-2" size={18} />
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </section>

        {/* Footer */}
        <footer className="relative bg-black/50 backdrop-blur-sm py-12 px-6 border-t border-gray-800 pb-24 z-10">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center mb-6 md:mb-0">
                <Logo size="medium" showText={true} />
              </div>
              
              <div className="flex items-center space-x-6 text-gray-400">
                <a 
                  href="#" 
                  className="hover:text-yellow-400 transition-colors duration-300"
                  tabIndex={0}
                  aria-label="Visit our Instagram page"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.40z"/>
                  </svg>
                </a>
                <a 
                  href="#" 
                  className="hover:text-yellow-400 transition-colors duration-300"
                  tabIndex={0}
                  aria-label="Visit our Facebook page"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
              </div>
            </div>
            
            <div className="border-t border-gray-800 mt-8 pt-8 text-center">
              <p className="text-gray-500 text-sm">
                © 2025 Tezz Designs. All rights reserved. | Professional CarX livery design services
              </p>
            </div>
          </div>
        </footer>

        {/* Dock Navigation - Fixed at bottom */}
        <div className="fixed bottom-0 left-0 right-0 z-50 pointer-events-none">
          <div className="pointer-events-auto">
            <Dock 
              items={dockItems}
              panelHeight={isMobile ? 60 : 68}
              baseItemSize={isMobile ? 44 : 50}
              magnification={isMobile ? 60 : 70}
              distance={isMobile ? 150 : 200}
            />
          </div>
        </div>
      </main>
    </>
  );
};

export default RequestLiveryPage; 